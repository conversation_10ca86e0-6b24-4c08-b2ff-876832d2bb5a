<?php

namespace App\Http\Controllers;

use App\Models\CustomerFollowup;
use App\Models\CustomerFollowupImage;
use App\Models\Customer;
use App\Services\ImageUploadService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CustomerFollowupController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index($customer_id)
    {
        $customer = Customer::findOrFail($customer_id);
        return view('customer_followups.index', compact('customer'));
    }

    public function datatable($customer_id)
    {
        $customer = Customer::findOrFail($customer_id);
        $query = $customer->customerFollowups();

        // DataTables parametrelerini al
        $draw = request('draw');
        $start = request('start', 0);
        $length = request('length', 10);
        $searchValue = request('search.value');
        $orderColumn = request('order.0.column', 0);
        $orderDir = request('order.0.dir', 'desc');

        // Sütun isimleri
        $columns = ['id', 'track_date', 'status', 'note', 'agreement_status'];

        // Genel arama
        if ($searchValue) {
            $query->where(function($q) use ($searchValue) {
                $q->where('note', 'ilike', "%{$searchValue}%")
                  ->orWhere('status', 'ilike', "%{$searchValue}%");
            });
        }

        // Sütun bazlı arama
        for ($i = 0; $i < count($columns); $i++) {
            $columnSearch = request("columns.{$i}.search.value");
            if ($columnSearch && isset($columns[$i])) {
                $column = $columns[$i];
                $query->where($column, 'ilike', "%{$columnSearch}%");
            }
        }

        // Toplam kayıt sayısı
        $totalRecords = $customer->customerFollowups()->count();
        $filteredRecords = $query->count();

        // Sıralama
        if (isset($columns[$orderColumn])) {
            $query->orderBy($columns[$orderColumn], $orderDir);
        }

        // Sayfalama
        $followups = $query->skip($start)->take($length)->get();

        // Veriyi formatla
        $data = [];
        foreach ($followups as $followup) {
            $data[] = [
                $followup->id,
                $followup->track_date,
                $followup->status,
                \Str::limit($followup->note, 50),
                $followup->agreement_status === null ? '' : ($followup->agreement_status ? 'Evet' : 'Hayır'),
                '<a href="' . route('customers.customer-followups.show', [$customer->id, $followup->id]) . '" class="btn btn-info btn-sm">Detay</a> ' .
                '<a href="' . route('customers.customer-followups.edit', [$customer->id, $followup->id]) . '" class="btn btn-warning btn-sm">Düzenle</a> ' .
                '<form action="' . route('customers.customer-followups.destroy', [$customer->id, $followup->id]) . '" method="POST" style="display:inline-block;">' .
                csrf_field() . method_field('DELETE') .
                '<button type="submit" class="btn btn-danger btn-sm" onclick="return confirm(\'Silmek istediğinize emin misiniz?\')">Sil</button></form>'
            ];
        }

        return response()->json([
            'draw' => intval($draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Display all customer followups.
     */
    public function allFollowups(Request $request)
    {
        return view('customer_followups.all_followups');
    }

    public function allFollowupsDatatable()
    {
        $query = CustomerFollowup::with('customer');

        // DataTables parametrelerini al
        $draw = request('draw');
        $start = request('start', 0);
        $length = request('length', 10);
        $searchValue = request('search.value');
        $orderColumn = request('order.0.column', 0);
        $orderDir = request('order.0.dir', 'desc');

        // Sütun isimleri
        $columns = ['id', 'customer', 'track_date', 'meet_date', 'status', 'conversation_type', 'work_type', 'user_name', 'city', 'district', 'note', 'agreement_status', 'pluscard_been_loaded'];

        // Genel arama
        if ($searchValue) {
            $query->where(function($q) use ($searchValue) {
                $q->where('note', 'ilike', "%{$searchValue}%")
                  ->orWhere('status', 'ilike', "%{$searchValue}%")
                  ->orWhere('conversation_type', 'ilike', "%{$searchValue}%")
                  ->orWhere('work_type', 'ilike', "%{$searchValue}%")
                  ->orWhere('user_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('city', 'ilike', "%{$searchValue}%")
                  ->orWhere('district', 'ilike', "%{$searchValue}%")
                  ->orWhereHas('customer', function($q) use ($searchValue) {
                      $q->where('company_name', 'ilike', "%{$searchValue}%");
                  });
            });
        }

        // Sütun bazlı arama
        for ($i = 0; $i < count($columns); $i++) {
            $columnSearch = request("columns.{$i}.search.value");
            if ($columnSearch && isset($columns[$i])) {
                $column = $columns[$i];
                if ($column === 'customer') {
                    $query->whereHas('customer', function($q) use ($columnSearch) {
                        $q->where('company_name', 'ilike', "%{$columnSearch}%");
                    });
                } elseif ($column !== 'customer') {
                    $query->where($column, 'ilike', "%{$columnSearch}%");
                }
            }
        }

        // Toplam kayıt sayısı
        $totalRecords = CustomerFollowup::count();
        $filteredRecords = $query->count();

        // Sıralama
        if (isset($columns[$orderColumn]) && $columns[$orderColumn] !== 'customer') {
            $query->orderBy($columns[$orderColumn], $orderDir);
        } elseif ($columns[$orderColumn] === 'customer') {
            $query->join('customers', 'customer_followups.customer_id', '=', 'customers.id')
                  ->orderBy('customers.company_name', $orderDir)
                  ->select('customer_followups.*');
        }

        // Sayfalama
        $followups = $query->skip($start)->take($length)->get();

        // Veriyi formatla
        $data = [];
        foreach ($followups as $followup) {
            $data[] = [
                $followup->id,
                '<a href="' . route('customers.show', $followup->customer->id) . '" class="text-decoration-none">' . ($followup->customer->company_name ?? $followup->customer->name) . '</a>',
                \Carbon\Carbon::parse($followup->track_date)->format('d.m.Y'),
                $followup->meet_date ? \Carbon\Carbon::parse($followup->meet_date)->format('d.m.Y') : '-',
                '<span class="badge badge-' . ($followup->status == 'aktif' ? 'success' : ($followup->status == 'pasif' ? 'warning' : 'secondary')) . '">' . $followup->getStatusDisplayAttribute() . '</span>',
                '<span class="badge badge-info">' . ucfirst($followup->conversation_type) . '</span>',
                $followup->work_type ?? '-',
                $followup->user_name ?? '-',
                $followup->city ?? '-',
                $followup->district ?? '-',
                \Str::limit($followup->note, 30),
                $followup->agreement_status === null ? '<span class="text-muted">-</span>' : '<span class="badge badge-' . ($followup->agreement_status ? 'success' : 'danger') . '">' . ($followup->agreement_status ? 'Evet' : 'Hayır') . '</span>',
                $followup->pluscard_been_loaded === null ? '<span class="text-muted">-</span>' : '<span class="badge badge-' . ($followup->pluscard_been_loaded ? 'success' : 'warning') . '">' . ($followup->pluscard_been_loaded ? 'Evet' : 'Hayır') . '</span>',
                '<a href="' . route('customers.customer-followups.show', [$followup->customer->id, $followup->id]) . '" class="btn btn-info btn-sm" title="Detay"><i class="fas fa-eye"></i></a> ' .
                '<a href="' . route('customers.customer-followups.edit', [$followup->customer->id, $followup->id]) . '" class="btn btn-warning btn-sm" title="Düzenle"><i class="fas fa-edit"></i></a> '
            ];
        }

        return response()->json([
            'draw' => intval($draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create($customer_id)
    {
        $customer = Customer::findOrFail($customer_id);
        $authUser = auth()->user();
        
        // Pre-fill data from customer and authenticated user
        $prefill = [
            'city' => $customer->city,
            'district' => $customer->district,
            'branch_name' => $customer->company_name, // Using company_name as branch_name
            'user_name' => $authUser ? $authUser->name : '',
            'company_name' => $customer->company_name,
            'mail' => $customer->email,
        ];
        
        return view('customer_followups.create', compact('customer', 'prefill'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, $customer_id)
    {
        $customer = Customer::findOrFail($customer_id);

        try {
            $validated = $request->validate([
            'track_date' => 'required|date',
            'note' => 'required|string',
            'status' => CustomerFollowup::getStatusValidationRule(),
            'conversation_type' => 'required|in:telefon,yerinde,bayide',
            'pluscard_been_loaded' => 'required|in:0,1',
            'number_of_customers_loaded' => 'nullable|required_if:pluscard_been_loaded,1|integer|min:1',
            'loading_amount' => 'nullable|required_if:pluscard_been_loaded,1|numeric|min:0',
            'reason_not_understanding' => 'nullable|required_if:pluscard_been_loaded,0|string',
            'contact_first_name' => 'nullable|string|max:100',
            'contact_last_name' => 'nullable|string|max:100',
            'contact_title' => 'nullable|string|max:100',
            'contact_phone' => 'nullable|string|max:30',
            'contact_email' => 'nullable|email|max:100',
            'current_branch_count' => 'nullable|integer|min:0',
            'branch_potential' => 'nullable|integer|min:0',
            'agreement_status' => 'nullable|in:0,1',
            'city' => 'nullable|string|max:100',
            'district' => 'nullable|string|max:100',
            'branch_name' => 'nullable|string|max:255',
            'meet_date' => 'nullable|date',
            'user_name' => 'nullable|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'mail' => 'nullable|email|max:100',
            'work_type' => CustomerFollowup::getWorkTypeValidationRule(),
            'current_firm' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'status_at_meeting_date' => CustomerFollowup::getStatusAtMeetingDateValidationRule(),
            'images' => 'nullable|array|max:10',
            'images.*' => 'image|mimes:jpeg,jpg,png,gif,webp|max:10240', // 10MB max per image
        ]);

        $images = $validated['images'] ?? [];
        unset($validated['images']);

        $validated['customer_id'] = $customer->id;
        $followup = CustomerFollowup::create($validated);

        // Resimleri yükle
        if (!empty($images)) {
            $imageUploadService = new ImageUploadService();
            try {
                $uploadedImages = $imageUploadService->uploadMultiple($images, 'customer-followups');

                foreach ($uploadedImages as $index => $imageData) {
                    $followup->images()->create([
                        'filename' => $imageData['filename'],
                        'path' => $imageData['path'],
                        'mime_type' => $imageData['mime_type'],
                        'size' => $imageData['size'],
                        'width' => $imageData['width'],
                        'height' => $imageData['height'],
                        'sort_order' => $index,
                    ]);
                }
            } catch (\Exception $e) {
                \Log::error('Customer followup image upload error: ' . $e->getMessage(), [
                    'followup_id' => $followup->id,
                    'trace' => $e->getTraceAsString()
                ]);

                return redirect()->back()
                    ->withInput()
                    ->withErrors(['images' => 'Resim yükleme sırasında hata oluştu: ' . $e->getMessage()]);
            }
        }

            return redirect()->route('customers.customer-followups.index', $customer->id)->with('success', 'Takip kaydı eklendi!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Customer followup validation error: ', [
                'errors' => $e->errors(),
                'customer_id' => $customer_id,
                'request_data' => $request->all()
            ]);

            return redirect()->back()
                ->withInput()
                ->withErrors($e->errors());

        } catch (\Exception $e) {
            \Log::error('Customer followup store error: ' . $e->getMessage(), [
                'customer_id' => $customer_id,
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Takip kaydı oluşturulurken hata oluştu: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($customer_id, $id)
    {
        $customer = Customer::findOrFail($customer_id);
        $followup = CustomerFollowup::with('images')->where('customer_id', $customer->id)->findOrFail($id);
        return view('customer_followups.show', compact('customer', 'followup'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($customer_id, $id)
    {
        $customer = Customer::findOrFail($customer_id);
        $followup = CustomerFollowup::where('customer_id', $customer->id)->findOrFail($id);
        return view('customer_followups.edit', compact('customer', 'followup'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $customer_id, $id)
    {
        $customer = Customer::findOrFail($customer_id);
        $followup = CustomerFollowup::where('customer_id', $customer->id)->findOrFail($id);
        $validated = $request->validate([
            'track_date' => 'required|date',
            'note' => 'required|string',
            'status' => CustomerFollowup::getStatusValidationRule(),
            'conversation_type' => 'required|in:telefon,yerinde,bayide',
            'pluscard_been_loaded' => 'required|in:0,1',
            'number_of_customers_loaded' => 'nullable|required_if:pluscard_been_loaded,1|integer|min:1',
            'loading_amount' => 'nullable|required_if:pluscard_been_loaded,1|numeric|min:0',
            'reason_not_understanding' => 'nullable|required_if:pluscard_been_loaded,0|string',
            'contact_first_name' => 'nullable|string|max:100',
            'contact_last_name' => 'nullable|string|max:100',
            'contact_title' => 'nullable|string|max:100',
            'contact_phone' => 'nullable|string|max:30',
            'contact_email' => 'nullable|email|max:100',
            'current_branch_count' => 'nullable|integer|min:0',
            'branch_potential' => 'nullable|integer|min:0',
            'agreement_status' => 'nullable|in:0,1',
            'city' => 'nullable|string|max:100',
            'district' => 'nullable|string|max:100',
            'branch_name' => 'nullable|string|max:255',
            'meet_date' => 'nullable|date',
            'user_name' => 'nullable|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'mail' => 'nullable|email|max:100',
            'work_type' => CustomerFollowup::getWorkTypeValidationRule(),
            'current_firm' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'status_at_meeting_date' => CustomerFollowup::getStatusAtMeetingDateValidationRule(),
        ]);
        $followup->update($validated);
        return redirect()->route('customers.customer-followups.index', $customer->id)->with('success', 'Takip kaydı güncellendi!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($customer_id, $id)
    {
        $customer = Customer::findOrFail($customer_id);
        $followup = CustomerFollowup::where('customer_id', $customer->id)->findOrFail($id);
        $followup->delete();
        return redirect()->route('customers.customer-followups.index', $customer->id)->with('success', 'Takip kaydı silindi!');
    }

    public function exportExcel($customer_id, Request $request)
    {
        $customer = Customer::findOrFail($customer_id);
        $query = $customer->customerFollowups();

        // Filtre parametrelerini al
        $searchValue = $request->get('search');

        // Genel arama
        if ($searchValue) {
            $query->where(function($q) use ($searchValue) {
                $q->where('note', 'ilike', "%{$searchValue}%")
                  ->orWhere('status', 'ilike', "%{$searchValue}%");
            });
        }

        // Sütun bazlı filtreler
        if ($request->has('columns')) {
            foreach ($request->get('columns') as $index => $column) {
                if (!empty($column['search']['value'])) {
                    $searchTerm = $column['search']['value'];
                    switch ($index) {
                        case 0: // ID
                            $query->where('id', 'like', "%{$searchTerm}%");
                            break;
                        case 1: // Track Date
                            $query->where('track_date', 'like', "%{$searchTerm}%");
                            break;
                        case 2: // Status
                            $query->where('status', 'ilike', "%{$searchTerm}%");
                            break;
                        case 3: // Note
                            $query->where('note', 'ilike', "%{$searchTerm}%");
                            break;
                        case 4: // Agreement Status
                            if (strtolower($searchTerm) === 'evet' || $searchTerm === '1') {
                                $query->where('agreement_status', '1');
                            } elseif (strtolower($searchTerm) === 'hayır' || $searchTerm === '0') {
                                $query->where('agreement_status', '0');
                            }
                            break;
                    }
                }
            }
        }

        $followups = $query->get();

        $filename = 'musteri_takipleri_' . $customer->company_name . '_' . date('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($followups) {
            $file = fopen('php://output', 'w');

            // UTF-8 BOM ekle
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // Header satırı
            fputcsv($file, [
                'ID',
                'Takip Tarihi',
                'Durum',
                'Not',
                'Anlaşma Durumu'
            ]);

            // Veri satırları
            foreach ($followups as $followup) {
                $agreementStatus = '';
                if ($followup->agreement_status === '1') {
                    $agreementStatus = 'Evet';
                } elseif ($followup->agreement_status === '0') {
                    $agreementStatus = 'Hayır';
                } else {
                    $agreementStatus = '-';
                }

                fputcsv($file, [
                    $followup->id,
                    $followup->track_date ? date('d.m.Y', strtotime($followup->track_date)) : '-',
                    $followup->status ?? '-',
                    $followup->note ?? '-',
                    $agreementStatus
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Delete a customer followup image.
     */
    public function deleteImage($customer_id, $followup_id, $image_id)
    {
        $customer = Customer::findOrFail($customer_id);
        $followup = CustomerFollowup::where('customer_id', $customer->id)->findOrFail($followup_id);
        $image = CustomerFollowupImage::where('customer_followup_id', $followup->id)->findOrFail($image_id);

        try {
            $image->delete(); // Model'deki boot metodu dosyayı da silecek

            return response()->json([
                'success' => true,
                'message' => 'Resim başarıyla silindi.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Resim silinirken hata oluştu: ' . $e->getMessage()
            ], 500);
        }
    }

}
