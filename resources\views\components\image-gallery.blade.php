@props(['images', 'title' => 'Resim Galerisi', 'showTitle' => true, 'allowDelete' => false, 'deleteRoute' => null])

@if($images && $images->count() > 0)
<div class="image-gallery-container">
    @if($showTitle)
    <h6 class="mb-3">{{ $title }}</h6>
    @endif
    
    <div class="row">
        @foreach($images as $image)
        <div class="col-md-3 col-sm-4 col-6 mb-3">
            <div class="image-gallery-item position-relative">
                <div class="image-wrapper" style="height: 200px; overflow: hidden; border-radius: 8px; cursor: pointer;">
                    <img src="{{ $image->url }}" 
                         alt="{{ $image->alt_text ?? $image->filename }}" 
                         class="img-fluid w-100 h-100 gallery-image"
                         style="object-fit: cover; transition: transform 0.3s ease;"
                         data-toggle="modal" 
                         data-target="#imageModal"
                         data-image-url="{{ $image->url }}"
                         data-image-title="{{ $image->filename }}"
                         data-image-description="{{ $image->description }}"
                         data-image-size="{{ $image->formatted_size }}"
                         data-image-dimensions="{{ $image->width }}x{{ $image->height }}">
                </div>
                
                @if($allowDelete && $deleteRoute)
                <button type="button" 
                        class="btn btn-danger btn-sm position-absolute delete-image-btn" 
                        style="top: 8px; right: 8px; padding: 4px 8px; z-index: 10;"
                        onclick="deleteImage({{ $image->id }}, '{{ $deleteRoute }}')"
                        title="Resmi Sil">
                    <i class="fas fa-trash"></i>
                </button>
                @endif
                
                <div class="image-info mt-2">
                    <small class="text-muted d-block">{{ $image->filename }}</small>
                    <small class="text-muted">{{ $image->formatted_size }}</small>
                    @if($image->description)
                    <small class="text-muted d-block">{{ Str::limit($image->description, 50) }}</small>
                    @endif
                </div>
            </div>
        </div>
        @endforeach
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Resim Görüntüleyici</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="" class="img-fluid" style="max-height: 70vh;">
                <div class="mt-3">
                    <h6 id="modalImageTitle"></h6>
                    <p id="modalImageDescription" class="text-muted"></p>
                    <small class="text-muted">
                        <span id="modalImageSize"></span> | 
                        <span id="modalImageDimensions"></span>
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <a id="modalImageDownload" href="" download class="btn btn-primary">
                    <i class="fas fa-download"></i> İndir
                </a>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Kapat</button>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.image-gallery-item:hover .image-wrapper img {
    transform: scale(1.05);
}

.image-gallery-item .delete-image-btn {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-gallery-item:hover .delete-image-btn {
    opacity: 1;
}

.gallery-image:hover {
    cursor: pointer;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Modal'ı açarken resim bilgilerini doldur
    $('.gallery-image').on('click', function() {
        const imageUrl = $(this).data('image-url');
        const imageTitle = $(this).data('image-title');
        const imageDescription = $(this).data('image-description');
        const imageSize = $(this).data('image-size');
        const imageDimensions = $(this).data('image-dimensions');
        
        $('#modalImage').attr('src', imageUrl).attr('alt', imageTitle);
        $('#modalImageTitle').text(imageTitle);
        $('#modalImageDescription').text(imageDescription || 'Açıklama bulunmuyor');
        $('#modalImageSize').text(imageSize);
        $('#modalImageDimensions').text(imageDimensions);
        $('#modalImageDownload').attr('href', imageUrl);
    });
});

// Resim silme fonksiyonu
function deleteImage(imageId, deleteRoute) {
    if (confirm('Bu resmi silmek istediğinizden emin misiniz?')) {
        $.ajax({
            url: deleteRoute.replace(':id', imageId),
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    location.reload(); // Sayfayı yenile
                } else {
                    alert('Resim silinirken hata oluştu: ' + (response.message || 'Bilinmeyen hata'));
                }
            },
            error: function(xhr) {
                alert('Resim silinirken hata oluştu: ' + (xhr.responseJSON?.message || 'Bilinmeyen hata'));
            }
        });
    }
}
</script>
@endpush

@else
<div class="text-center text-muted py-4">
    <i class="fas fa-images fa-3x mb-3"></i>
    <p>Henüz resim eklenmemiş.</p>
</div>
@endif
