<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Role;
use App\Models\Region;
use App\Models\Dealer;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Rolleri al
        $adminRole = Role::where('name', Role::ADMIN)->first();
        $managerRole = Role::where('name', Role::MANAGER)->first();
        $userRole = Role::where('name', Role::USER)->first();
        $branchManagerRole = Role::where('name', Role::BRANCH_MANAGER)->first();
        $salesRepRole = Role::where('name', Role::SALES_REPRESENTATIVE)->first();
        $regionalManagerRole = Role::where('name', Role::REGIONAL_MANAGER)->first();
        $salesManagerRole = Role::where('name', Role::SALES_MANAGER)->first();

        // Bölgeleri al
        $marmaraRegion = Region::where('name', 'Marmara Bölgesi')->first();
        $egeRegion = Region::where('name', 'Ege Bölgesi')->first();
        $akdenizRegion = Region::where('name', 'Akdeniz Bölgesi')->first();
        $icAnadoluRegion = Region::where('name', 'İç Anadolu Bölgesi')->first();

        // Bayileri al
        $dealers = Dealer::limit(5)->get();

        $users = [
            // Admin kullanıcılar
            [
                'name' => 'Sistem Yöneticisi',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('admin123'),
                'role_id' => $adminRole?->id,
                'region_id' => null,
            ],
            [
                'name' => 'Ahmet Yılmaz',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $adminRole?->id,
                'region_id' => $marmaraRegion?->id,
            ],

            // Müdürler
            [
                'name' => 'Mehmet Demir',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $managerRole?->id,
                'region_id' => $marmaraRegion?->id,
            ],
            [
                'name' => 'Ayşe Kaya',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $managerRole?->id,
                'region_id' => $egeRegion?->id,
            ],

            // Bölge Müdürleri
            [
                'name' => 'Fatma Özkan',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $regionalManagerRole?->id,
                'region_id' => $marmaraRegion?->id,
            ],
            [
                'name' => 'Ali Çelik',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $regionalManagerRole?->id,
                'region_id' => $egeRegion?->id,
            ],
            [
                'name' => 'Zeynep Arslan',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $regionalManagerRole?->id,
                'region_id' => $akdenizRegion?->id,
            ],

            // Satış Müdürleri
            [
                'name' => 'Hasan Polat',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $salesManagerRole?->id,
                'region_id' => $marmaraRegion?->id,
            ],
            [
                'name' => 'Elif Şahin',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $salesManagerRole?->id,
                'region_id' => $egeRegion?->id,
            ],

            // Şube Müdürleri
            [
                'name' => 'Murat Koç',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $branchManagerRole?->id,
                'region_id' => $marmaraRegion?->id,
            ],
            [
                'name' => 'Seda Aydın',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $branchManagerRole?->id,
                'region_id' => $egeRegion?->id,
            ],
            [
                'name' => 'Emre Güneş',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $branchManagerRole?->id,
                'region_id' => $akdenizRegion?->id,
            ],

            // Satış Temsilcileri
            [
                'name' => 'Burak Yıldız',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $salesRepRole?->id,
                'region_id' => $marmaraRegion?->id,
            ],
            [
                'name' => 'Gamze Öztürk',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $salesRepRole?->id,
                'region_id' => $egeRegion?->id,
            ],
            [
                'name' => 'Kemal Doğan',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $salesRepRole?->id,
                'region_id' => $akdenizRegion?->id,
            ],
            [
                'name' => 'Pınar Kılıç',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $salesRepRole?->id,
                'region_id' => $icAnadoluRegion?->id,
            ],

            // Standart Kullanıcılar
            [
                'name' => 'Deniz Aktaş',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $userRole?->id,
                'region_id' => $marmaraRegion?->id,
            ],
            [
                'name' => 'Canan Ertürk',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $userRole?->id,
                'region_id' => $egeRegion?->id,
            ],
            [
                'name' => 'Oğuz Karaca',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $userRole?->id,
                'region_id' => $akdenizRegion?->id,
            ],
            [
                'name' => 'Sibel Yavuz',
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'password' => Hash::make('password123'),
                'role_id' => $userRole?->id,
                'region_id' => $icAnadoluRegion?->id,
            ],
        ];

        foreach ($users as $userData) {
            $user = User::create($userData);

            // Bazı kullanıcılara dealer ataması yap (many-to-many ilişki)
            if ($dealers->count() > 0 && in_array($user->role?->name, [Role::SALES_REPRESENTATIVE, Role::BRANCH_MANAGER])) {
                $randomDealers = $dealers->random(rand(1, min(3, $dealers->count())));
                $user->dealers()->attach($randomDealers->pluck('id'));
            }
        }
    }
}
