<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
// Intervention Image imports kaldırıldı - GD extension sorunu nedeniyle

class ImageUploadService
{
    protected $disk = 'public';
    protected $maxFileSize = 10485760; // 10MB in bytes (artırıldı çünkü resim işleme yok)
    protected $allowedMimeTypes = [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'image/webp'
    ];
    protected $maxWidth = 1920;
    protected $maxHeight = 1080;

    /**
     * Upload and process multiple images.
     *
     * @param array $files Array of UploadedFile objects
     * @param string $directory Directory to store images
     * @return array Array of processed image data
     */
    public function uploadMultiple(array $files, string $directory = 'images'): array
    {
        $uploadedImages = [];

        foreach ($files as $file) {
            if ($file instanceof UploadedFile && $file->isValid()) {
                try {
                    $imageData = $this->uploadSingle($file, $directory);
                    $uploadedImages[] = $imageData;
                } catch (\Exception $e) {
                    // Log error but continue with other files
                    \Log::error('Image upload failed: ' . $e->getMessage());
                    continue;
                }
            }
        }

        return $uploadedImages;
    }

    /**
     * Upload and store a single image without processing.
     *
     * @param UploadedFile $file
     * @param string $directory
     * @return array
     */
    public function uploadSingle(UploadedFile $file, string $directory = 'images'): array
    {
        $this->validateFile($file);

        // Generate unique filename
        $filename = $this->generateFilename($file);
        $path = $directory . '/' . $filename;

        // Store the file directly without image processing
        Storage::disk($this->disk)->putFileAs($directory, $file, $filename);

        // Get image dimensions using getimagesize (built-in PHP function)
        $imageInfo = @getimagesize($file->getPathname());
        $width = $imageInfo[0] ?? null;
        $height = $imageInfo[1] ?? null;

        return [
            'filename' => $file->getClientOriginalName(),
            'path' => $path,
            'mime_type' => $file->getClientMimeType(),
            'size' => $file->getSize(),
            'width' => $width,
            'height' => $height,
        ];
    }

    /**
     * Validate uploaded file.
     *
     * @param UploadedFile $file
     * @throws \InvalidArgumentException
     */
    protected function validateFile(UploadedFile $file): void
    {
        // Check file size
        if ($file->getSize() > $this->maxFileSize) {
            throw new \InvalidArgumentException('Dosya boyutu çok büyük. Maksimum ' . ($this->maxFileSize / 1024 / 1024) . 'MB olmalıdır.');
        }

        // Check mime type
        if (!in_array($file->getClientMimeType(), $this->allowedMimeTypes)) {
            throw new \InvalidArgumentException('Geçersiz dosya tipi. Sadece JPEG, PNG, GIF ve WebP dosyaları kabul edilir.');
        }

        // Check if file is actually an image
        $imageInfo = getimagesize($file->getPathname());
        if ($imageInfo === false) {
            throw new \InvalidArgumentException('Dosya geçerli bir resim dosyası değil.');
        }
    }

    /**
     * Generate unique filename.
     *
     * @param UploadedFile $file
     * @return string
     */
    protected function generateFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $name = Str::slug($name);
        
        return $name . '_' . time() . '_' . Str::random(8) . '.' . $extension;
    }

    /**
     * Delete image file.
     *
     * @param string $path
     * @return bool
     */
    public function deleteImage(string $path): bool
    {
        if (Storage::disk($this->disk)->exists($path)) {
            return Storage::disk($this->disk)->delete($path);
        }
        
        return true;
    }

    /**
     * Get image URL.
     *
     * @param string $path
     * @return string
     */
    public function getImageUrl(string $path): string
    {
        return Storage::disk($this->disk)->url($path);
    }

    /**
     * Set maximum file size.
     *
     * @param int $size Size in bytes
     * @return self
     */
    public function setMaxFileSize(int $size): self
    {
        $this->maxFileSize = $size;
        return $this;
    }

    /**
     * Set maximum dimensions.
     *
     * @param int $width
     * @param int $height
     * @return self
     */
    public function setMaxDimensions(int $width, int $height): self
    {
        $this->maxWidth = $width;
        $this->maxHeight = $height;
        return $this;
    }
}
