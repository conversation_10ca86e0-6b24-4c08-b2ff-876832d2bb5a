<?php $__env->startSection('content'); ?>
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title text-center fw-bold py-2">Müşteri Detayı</h3>
                </div>
                <div class="card-body">
                    
                    <!-- <PERSON>lik Bilgileri -->
                    <!--<h5 class="mb-3 text-primary">Kimlik Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">PlusCard No:</label>
                                <p class="mb-2"><?php echo e($customer->pluscard_no ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div> -->
                    

                    <!-- Firma Bilgileri -->
                    <h5 class="mb-3 text-primary">Firma Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Firma Adı:</label>
                                <p class="mb-2"><?php echo e($customer->company_name ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">E-posta:</label>
                                <p class="mb-2"><?php echo e($customer->email ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Web Sitesi:</label>
                                <p class="mb-2"><?php echo e($customer->website_url ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Google Haritalar</label>
                                <p class="mb-2"><?php echo e($customer->google_maps_url ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">İlan Sitesi</label>
                                <p class="mb-2"><?php echo e($customer->listing_url ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Yetkili Kişi Bilgileri -->
                    <h5 class="mb-3 text-primary">Yetkili Kişi Bilgileri</h5>

                    <?php
                        $allAuthorizedPersons = collect();

                        // Eski tek yetkili kişi bilgilerini ekle (eğer varsa)
                        if($customer->authorized_title || $customer->authorized_first_name || $customer->authorized_last_name || $customer->authorized_phone) {
                            $allAuthorizedPersons->push((object)[
                                'title' => $customer->authorized_title,
                                'first_name' => $customer->authorized_first_name,
                                'last_name' => $customer->authorized_last_name,
                                'phone' => $customer->authorized_phone,
                            ]);
                        }

                        // Yeni yetkili kişileri ekle
                        if($customer->authorizedPersons) {
                            $allAuthorizedPersons = $allAuthorizedPersons->merge($customer->authorizedPersons);
                        }
                    ?>

                    <?php if($allAuthorizedPersons->count() > 0): ?>
                        <?php $__currentLoopData = $allAuthorizedPersons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $person): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-secondary mb-3">Yetkili Kişi #<?php echo e($index + 1); ?></h6>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Ünvan:</label>
                                        <p class="mb-2"><?php echo e($person->title ?? 'Belirtilmemiş'); ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Telefon:</label>
                                        <p class="mb-2"><?php echo e($person->phone ?? 'Belirtilmemiş'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili İsim:</label>
                                        <p class="mb-2"><?php echo e($person->first_name ?? 'Belirtilmemiş'); ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Soyisim:</label>
                                        <p class="mb-2"><?php echo e($person->last_name ?? 'Belirtilmemiş'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <?php if(!$loop->last): ?>
                                <hr class="my-4">
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="info-group">
                                    <p class="mb-2 text-muted">Yetkili kişi bilgisi bulunmamaktadır.</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- İletişim Bilgileri -->
                    <h5 class="mb-3 text-primary">İletişim Bilgileri</h5>

                    <!-- Şirket Telefonları -->
                    <div class="mb-4">
                        <h6 class="mb-3 text-secondary">Şirket Telefonları</h6>
                        <?php
                            $allPhones = collect();

                            if(method_exists($customer, 'phones') && $customer->phones) {
                                $newPhones = $customer->phones;
                                foreach($newPhones as $phone) {
                                    $allPhones->push($phone);
                                }
                            }
                        ?>

                        <?php if($allPhones->count() > 0): ?>
                            <div class="row">
                                <?php $__currentLoopData = $allPhones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $phone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-start border-primary border-3 h-100 shadow-sm">
                                            <div class="card-body py-3">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div class="flex-grow-1">
                                                        <h6 class="text-primary mb-2">
                                                            <i class="fas fa-phone me-2"></i>Telefon #<?php echo e($index + 1); ?>

                                                        </h6>
                                                        <p class="mb-1 fw-bold fs-6">
                                                            <a href="tel:<?php echo e($phone->phone); ?>" class="text-decoration-none text-dark">
                                                                <?php echo e($phone->phone); ?>

                                                            </a>
                                                        </p>
                                                        <small class="text-muted">
                                                            <i class="fas fa-tag me-1"></i><?php echo e($phone->type); ?>

                                                        </small>
                                                    </div>
                                                    <div class="text-end">
                                                        <span class="badge bg-<?php echo e($phone->type == 'Mobil' ? 'success' : ($phone->type == 'Fax' ? 'warning' : ($phone->type == 'Diğer' ? 'info' : 'primary'))); ?> mb-2">
                                                            <?php echo e($phone->type); ?>

                                                        </span>
                                                        <br>
                                                        <a href="tel:<?php echo e($phone->phone); ?>" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-phone"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php if(($index + 1) % 2 == 0): ?>
                                        </div><div class="row">
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Henüz şirket telefonu eklenmemiş.
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Adres Bilgileri -->
                    <h5 class="mb-3 text-primary">Adres Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">İl:</label>
                                <p class="mb-2"><?php echo e($customer->city ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">İlçe:</label>
                                <p class="mb-2"><?php echo e($customer->district ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Adres:</label>
                                <p class="mb-2"><?php echo e($customer->address ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- Resim Galerisi Bölümü -->
                <div class="card-body">
                    <?php if (isset($component)) { $__componentOriginal1446d8c6d92ef292046237c6e60530d8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1446d8c6d92ef292046237c6e60530d8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.image-gallery','data' => ['images' => $customer->images,'title' => 'Müşteri Resimleri','allowDelete' => true,'deleteRoute' => ''.e(route('customers.images.delete', ['customer' => $customer->id, 'image' => ':id'])).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('image-gallery'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['images' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($customer->images),'title' => 'Müşteri Resimleri','allowDelete' => true,'deleteRoute' => ''.e(route('customers.images.delete', ['customer' => $customer->id, 'image' => ':id'])).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1446d8c6d92ef292046237c6e60530d8)): ?>
<?php $attributes = $__attributesOriginal1446d8c6d92ef292046237c6e60530d8; ?>
<?php unset($__attributesOriginal1446d8c6d92ef292046237c6e60530d8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1446d8c6d92ef292046237c6e60530d8)): ?>
<?php $component = $__componentOriginal1446d8c6d92ef292046237c6e60530d8; ?>
<?php unset($__componentOriginal1446d8c6d92ef292046237c6e60530d8); ?>
<?php endif; ?>
                </div>

                <div class="card-footer d-flex gap-2">
                    <a href="<?php echo e(route('customers.edit', $customer->id)); ?>" class="btn btn-primary flex-fill">Düzenle</a>
                    <a href="<?php echo e(route('customers.customer-followups.create', $customer->id)); ?>" class="btn btn-success flex-fill">Takip Ekle</a>
                    <a href="<?php echo e(route('customers.customer-branches.index', $customer->id)); ?>" class="btn btn-info flex-fill">Şubeleri Görüntüle</a>
                    <a href="<?php echo e(route('customers.index')); ?>" class="btn btn-secondary flex-fill">Geri Dön</a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-group {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 10px;
}

.info-group label {
    font-size: 0.9rem;
    margin-bottom: 5px;
    display: block;
}

.info-group p {
    font-size: 1rem;
    margin: 0;
    color: #333;
}

.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
}
</style>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/customers/show.blade.php ENDPATH**/ ?>