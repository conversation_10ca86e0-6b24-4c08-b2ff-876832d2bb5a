<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CustomerFollowup extends Model
{
    protected $fillable = [
        'customer_id',
        'track_date',
        'note',
        'status',
        'pluscard_been_loaded',
        'number_of_customers_loaded',
        'loading_amount',
        'reason_not_understanding',
        'conversation_type',
        'contact_first_name',
        'contact_last_name',
        'contact_title',
        'contact_phone',
        'contact_email',
        'current_branch_count',
        'branch_potential',
        'agreement_status',
        'city',
        'district',
        'branch_name',
        'meet_date',
        'user_name',
        'company_name',
        'mail',
        'work_type',
        'current_firm',
        'description',
        'status_at_meeting_date',
    ];

    protected $casts = [
        'track_date' => 'date',
        'meet_date' => 'date',
        'pluscard_been_loaded' => 'boolean',
        'agreement_status' => 'boolean',
        'current_branch_count' => 'integer',
        'branch_potential' => 'integer',
        'number_of_customers_loaded' => 'integer',
        'loading_amount' => 'decimal:2',
    ];

    // Status field validation rules
    public static $statusOptions = [
        'aktif' => 'Aktif',
        'pasif' => 'Pasif', 
        'eski müşteri' => 'Eski Müşteri',
        'hedef müşteri' => 'Hedef Müşteri'
    ];

    // Status at meeting date options
    public static $statusAtMeetingDateOptions = [
        'aktif' => 'Aktif',
        'sektör değişikliği' => 'Sektör Değişikliği',
        'taşınmış' => 'Taşınmış',
        'başka ekspertize gidiyor' => 'Başka Ekspertize Gidiyor',
        'cari değişikliği' => 'Cari Değişikliği',
        'birden fazla plus kart' => 'Birden Fazla Plus Kart',
        'diğer' => 'Diğer'
    ];

    // Work type options
    public static $workTypeOptions = [
        'PlusCard' => 'PlusCard',
        'Kurumsal' => 'Kurumsal',
        'Filo' => 'Filo'
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the images for the customer followup.
     */
    public function images()
    {
        return $this->hasMany(CustomerFollowupImage::class)->ordered();
    }

    // Status field validation
    public static function getStatusValidationRule()
    {
        return 'required|in:' . implode(',', array_keys(self::$statusOptions));
    }

    // Status at meeting date validation
    public static function getStatusAtMeetingDateValidationRule()
    {
        return 'nullable|in:' . implode(',', array_keys(self::$statusAtMeetingDateOptions));
    }

    // Work type validation
    public static function getWorkTypeValidationRule()
    {
        return 'required|in:' . implode(',', array_keys(self::$workTypeOptions));
    }

    // Get status display name
    public function getStatusDisplayAttribute()
    {
        return self::$statusOptions[$this->status] ?? $this->status;
    }

    // Scope for filtering by status
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // Scope for active customers
    public function scopeActive($query)
    {
        return $query->where('status', 'aktif');
    }

    // Scope for passive customers
    public function scopePassive($query)
    {
        return $query->where('status', 'pasif');
    }

    // Scope for old customers
    public function scopeOldCustomers($query)
    {
        return $query->where('status', 'eski müşteri');
    }
}
