<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_images', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->string('filename'); // Orijinal dosya adı
            $table->string('path'); // Storage'daki dosya yolu
            $table->string('mime_type'); // Dosya tipi (image/jpeg, image/png, etc.)
            $table->unsignedInteger('size'); // Dosya boyutu (bytes)
            $table->unsignedInteger('width')->nullable(); // Resim genişliği
            $table->unsignedInteger('height')->nullable(); // Resim yüksekliği
            $table->string('alt_text')->nullable(); // Alt text for accessibility
            $table->text('description')->nullable(); // Resim açıklaması
            $table->unsignedTinyInteger('sort_order')->default(0); // Sıralama
            $table->timestamps();
            
            $table->index(['customer_id', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_images');
    }
};
