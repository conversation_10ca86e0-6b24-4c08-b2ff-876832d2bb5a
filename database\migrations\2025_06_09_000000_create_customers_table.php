<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('email')->unique();
            $table->string('authorized_title');
            $table->string('authorized_first_name')->nullable();
            $table->string('authorized_last_name')->nullable();
            $table->string('authorized_phone')->nullable();
            $table->string('company_name')->nullable();
            $table->string('city');
            $table->string('district');
            $table->text('address');
            $table->string('tc')->unique()->nullable()->after('lastname');
            $table->string('pluscard_no')->unique()->nullable()->after('tc');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
}; 