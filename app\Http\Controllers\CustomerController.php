<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\CustomerImage;
use App\Models\CustomerPhone;
use App\Services\ImageUploadService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class CustomerController extends Controller
{
    public function index()
    {
        return view('customers.index');
    }

    public function datatable()
    {
        $query = \App\Models\Customer::with(['phones', 'authorizedPersons']);

        // DataTables parametrelerini al
        $draw = request('draw');
        $start = request('start', 0);
        $length = request('length', 10);
        $searchValue = request('search.value');
        $orderColumn = request('order.0.column', 0);
        $orderDir = request('order.0.dir', 'desc');

        // Sütun isimleri
        $columns = ['id', 'email', 'authorized_title', 'authorized_first_name', 'authorized_last_name', 'authorized_phone', 'company_name', 'phones', 'city', 'district', 'created_at'];

        // Genel arama
        if ($searchValue) {
            $query->where(function($q) use ($searchValue) {
                $q->where('email', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_title', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_first_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_last_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_phone', 'ilike', "%{$searchValue}%")
                  ->orWhere('company_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('city', 'ilike', "%{$searchValue}%")
                  ->orWhere('district', 'ilike', "%{$searchValue}%")
                  ->orWhere('address', 'ilike', "%{$searchValue}%");
            });
        }

        // Sütun bazlı arama
        for ($i = 0; $i < count($columns); $i++) {
            $columnSearch = request("columns.{$i}.search.value");
            if ($columnSearch && isset($columns[$i])) {
                $column = $columns[$i];
                if ($column !== 'phones') {
                    $query->where($column, 'ilike', "%{$columnSearch}%");
                }
            }
        }

        // Toplam kayıt sayısı
        $totalRecords = \App\Models\Customer::count();
        $filteredRecords = $query->count();

        // Sıralama
        if (isset($columns[$orderColumn])) {
            $query->orderBy($columns[$orderColumn], $orderDir);
        }

        // Sayfalama
        $customers = $query->skip($start)->take($length)->get();

        // Veriyi formatla
        $data = [];
        foreach ($customers as $customer) {
            // Telefon bilgilerini topla
            $allPhones = collect();
            if ($customer->phones && $customer->phones->count() > 0) {
                foreach ($customer->phones as $phone) {
                    $allPhones->push($phone->phone . ' (' . $phone->type . ')');
                }
            }
            $phonesText = $allPhones->count() > 0 ? $allPhones->implode('<br>') : '<small class="text-muted">Telefon yok</small>';

            $data[] = [
                $customer->id,
                '<a href="' . route('customers.show', $customer->id) . '" class="btn btn-info btn-sm" title="Detay"><i class="fas fa-eye"></i></a> ' .
                $customer->email,
                $customer->authorized_title ?? '-',
                $customer->authorized_first_name ?? '-',
                $customer->authorized_last_name ?? '-',
                $customer->authorized_phone ?? '-',
                $customer->company_name ?? '-',
                $phonesText,
                $customer->city ?? '-',
                $customer->district ?? '-',
                $customer->created_at ? $customer->created_at->format('d.m.Y') : '-',
                
                '<a href="' . route('customers.show', $customer->id) . '" class="btn btn-info btn-sm" title="Görüntüle"><i class="fas fa-eye"></i></a> ' .
                '<a href="' . route('customers.edit', $customer->id) . '" class="btn btn-warning btn-sm" title="Düzenle"><i class="fas fa-edit"></i></a> ' .
                '<a href="' . route('customers.customer-followups.index', $customer->id) . '" class="btn btn-secondary btn-sm" title="Takip Formu"><i class="fas fa-clipboard-list"></i> Takip</a>'
            ];
        }

        return response()->json([
            'draw' => intval($draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    public function create()
    {
        $dealers = \App\Models\Dealer::with('regions')->active()->orderBy('name')->get();
        return view('customers.create', compact('dealers'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([

            'email' => 'required|email|max:100|unique:customers,email',
            'tc' => 'nullable|string|max:11|unique:customers,tc',
            'pluscard_no' => 'nullable|string|max:40|unique:customers,pluscard_no',
            'authorized_title' => 'nullable|string|min:2|max:255',
            'authorized_first_name' => 'nullable|string|min:2|max:255',
            'authorized_last_name' => 'nullable|string|min:2|max:255',
            'authorized_phone' => 'nullable|string|min:10|max:20',
            'authorized_persons' => 'nullable|array',
            'authorized_persons.*.title' => 'nullable|string|max:255',
            'authorized_persons.*.first_name' => 'nullable|string|max:255',
            'authorized_persons.*.last_name' => 'nullable|string|max:255',
            'authorized_persons.*.phone' => 'nullable|string|max:20',
            'company_phones' => 'nullable|array',
            'company_phones.*.phone' => 'nullable|string|max:20',
            'company_phones.*.type' => 'nullable|string|in:Sabit,Mobil,Fax,Diğer',
            'company_name' => 'nullable|string|max:255',
            'city' => 'required|string|min:2|max:50',
            'district' => 'required|string|min:2|max:50',
            'address' => 'required|string|min:2|max:255',
            'dealer_id' => 'nullable|exists:dealers,id',
            'website_url' => 'nullable|url|max:255',
            'google_maps_url' => 'nullable|url|max:255',
            'listing_url' => 'nullable|url|max:255',
            'images' => 'nullable|array|max:10',
            'images.*' => 'image|mimes:jpeg,jpg,png,gif,webp|max:10240', // 10MB max per image
            'branches' => 'nullable|array',
            'branches.*.branch_name' => 'required_with:branches|string|max:255',
            'branches.*.email' => 'nullable|email|max:255',
            'branches.*.phone' => 'nullable|string|max:20',
            'branches.*.city' => 'nullable|string|max:100',
            'branches.*.district' => 'nullable|string|max:100',
            'branches.*.address' => 'nullable|string|max:1000',
        ]);

        $authorizedPersons = $validated['authorized_persons'] ?? [];
        $companyPhones = $validated['company_phones'] ?? [];
        $images = $validated['images'] ?? [];
        $branches = $validated['branches'] ?? [];
        unset($validated['authorized_persons']);
        unset($validated['company_phones']);
        unset($validated['images']);
        unset($validated['branches']);

        $customer = \App\Models\Customer::create($validated);

        // Yetkili kişileri ekle
        foreach ($authorizedPersons as $person) {
            if (!empty(trim($person['first_name'] ?? '')) ||
                !empty(trim($person['last_name'] ?? '')) ||
                !empty(trim($person['title'] ?? '')) ||
                !empty(trim($person['phone'] ?? ''))) {

                $customer->authorizedPersons()->create([
                    'title' => trim($person['title'] ?? ''),
                    'first_name' => trim($person['first_name'] ?? ''),
                    'last_name' => trim($person['last_name'] ?? ''),
                    'phone' => trim($person['phone'] ?? ''),
                ]);
            }
        }

        // Şirket telefonlarını ekle
        foreach ($companyPhones as $phone) {
            if (!empty(trim($phone['phone'] ?? ''))) {
                $customer->phones()->create([
                    'phone' => trim($phone['phone']),
                    'type' => trim($phone['type'] ?? 'Sabit'),
                ]);
            }
        }

        // Şubeleri ekle
        foreach ($branches as $branch) {
            if (!empty(trim($branch['branch_name'] ?? ''))) {
                $customer->branches()->create([
                    'branch_name' => trim($branch['branch_name']),
                    'email' => trim($branch['email'] ?? ''),
                    'phone' => trim($branch['phone'] ?? ''),
                    'city' => trim($branch['city'] ?? ''),
                    'district' => trim($branch['district'] ?? ''),
                    'address' => trim($branch['address'] ?? ''),
                ]);
            }
        }

        // Resimleri yükle
        if (!empty($images)) {
            try {
                $imageUploadService = new ImageUploadService();
                $uploadedImages = $imageUploadService->uploadMultiple($images, 'customers');

                foreach ($uploadedImages as $index => $imageData) {
                    $customer->images()->create([
                        'filename' => $imageData['filename'],
                        'path' => $imageData['path'],
                        'mime_type' => $imageData['mime_type'],
                        'size' => $imageData['size'],
                        'width' => $imageData['width'],
                        'height' => $imageData['height'],
                        'sort_order' => $index,
                    ]);
                }
            } catch (\Exception $e) {
                \Log::error('Customer image upload error: ' . $e->getMessage(), [
                    'customer_id' => $customer->id,
                    'trace' => $e->getTraceAsString()
                ]);

                return redirect()->back()
                    ->withInput()
                    ->withErrors(['images' => 'Resim yükleme sırasında hata oluştu: ' . $e->getMessage()]);
            }
        }

        return redirect()->route('customers.index')->with('success', 'Müşteri başarıyla eklendi!');
    }

    public function show($id)
    {
        $customer = \App\Models\Customer::with(['phones', 'authorizedPersons', 'images', 'branches', 'dealer.regions'])->findOrFail($id);
        return view('customers.show', compact('customer'));
    }

    public function edit($id)
    {
        $customer = \App\Models\Customer::with(['phones', 'authorizedPersons', 'dealer.regions', 'images', 'branches'])->findOrFail($id);
        $dealers = \App\Models\Dealer::with('regions')->active()->orderBy('name')->get();
        return view('customers.edit', compact('customer', 'dealers'));
    }

    public function update(Request $request, $id)
    {
        $customer = \App\Models\Customer::findOrFail($id);
        $validated = $request->validate([
            'email' => 'required|email|max:100|unique:customers,email,' . $customer->id,
            'tc' => 'nullable|string|max:11|unique:customers,tc,' . $customer->id,
            'pluscard_no' => 'nullable|string|max:40|unique:customers,pluscard_no,' . $customer->id,
            'authorized_title' => 'nullable|string|min:2|max:255',
            'authorized_first_name' => 'nullable|string|min:2|max:255',
            'authorized_last_name' => 'nullable|string|min:2|max:255',
            'authorized_phone' => 'nullable|string|min:10|max:20',
            'authorized_persons' => 'nullable|array',
            'authorized_persons.*.title' => 'nullable|string|max:255',
            'authorized_persons.*.first_name' => 'nullable|string|max:255',
            'authorized_persons.*.last_name' => 'nullable|string|max:255',
            'authorized_persons.*.phone' => 'nullable|string|max:20',
            'company_phones' => 'nullable|array',
            'company_phones.*.phone' => 'nullable|string|max:20',
            'company_phones.*.type' => 'nullable|string|in:Sabit,Mobil,Fax,Diğer',
            'company_phones.*.id' => 'nullable|integer|exists:customer_phones,id',
            'company_name' => 'nullable|string|max:255',
            'city' => 'required|string|min:2|max:50',
            'district' => 'required|string|min:2|max:50',
            'address' => 'required|string|min:2|max:255',
            'dealer_id' => 'nullable|exists:dealers,id',
            'website_url' => 'nullable|url|max:255',
            'google_maps_url' => 'nullable|url|max:255',
            'listing_url' => 'nullable|url|max:255',
            'images' => 'nullable|array|max:10',
            'images.*' => 'image|mimes:jpeg,jpg,png,gif,webp|max:10240', // 10MB max per image
        ]);

        $authorizedPersons = $validated['authorized_persons'] ?? [];
        $companyPhones = $validated['company_phones'] ?? [];
        $images = $validated['images'] ?? [];
        unset($validated['authorized_persons']);
        unset($validated['company_phones']);
        unset($validated['images']);

        $customer->update($validated);

        // Mevcut yetkili kişileri sil ve yenilerini ekle
        $customer->authorizedPersons()->delete();

        foreach ($authorizedPersons as $person) {
            if (!empty(trim($person['first_name'] ?? '')) ||
                !empty(trim($person['last_name'] ?? '')) ||
                !empty(trim($person['title'] ?? '')) ||
                !empty(trim($person['phone'] ?? ''))) {

                $customer->authorizedPersons()->create([
                    'title' => trim($person['title'] ?? ''),
                    'first_name' => trim($person['first_name'] ?? ''),
                    'last_name' => trim($person['last_name'] ?? ''),
                    'phone' => trim($person['phone'] ?? ''),
                ]);
            }
        }

        // Şirket telefonlarını güncelle
        $existingPhoneIds = [];
        foreach ($companyPhones as $phone) {
            if (!empty(trim($phone['phone'] ?? ''))) {
                if (!empty($phone['id'])) {
                    // Mevcut telefonu güncelle
                    $existingPhone = $customer->phones()->find($phone['id']);
                    if ($existingPhone) {
                        $existingPhone->update([
                            'phone' => trim($phone['phone']),
                            'type' => trim($phone['type'] ?? 'Sabit'),
                        ]);
                        $existingPhoneIds[] = $phone['id'];
                    }
                } else {
                    // Yeni telefon ekle
                    $newPhone = $customer->phones()->create([
                        'phone' => trim($phone['phone']),
                        'type' => trim($phone['type'] ?? 'Sabit'),
                    ]);
                    $existingPhoneIds[] = $newPhone->id;
                }
            }
        }

        // Formda olmayan telefonları sil
        $customer->phones()->whereNotIn('id', $existingPhoneIds)->delete();

        // Yeni resimleri yükle
        if (!empty($images)) {
            try {
                $imageUploadService = new ImageUploadService();
                $uploadedImages = $imageUploadService->uploadMultiple($images, 'customers');

                foreach ($uploadedImages as $index => $imageData) {
                    // Mevcut resimlerin en yüksek sort_order'ını al
                    $maxSortOrder = $customer->images()->max('sort_order') ?? -1;

                    $customer->images()->create([
                        'filename' => $imageData['filename'],
                        'path' => $imageData['path'],
                        'mime_type' => $imageData['mime_type'],
                        'size' => $imageData['size'],
                        'width' => $imageData['width'],
                        'height' => $imageData['height'],
                        'sort_order' => $maxSortOrder + $index + 1,
                    ]);
                }
            } catch (\Exception $e) {
                \Log::error('Customer image upload error: ' . $e->getMessage(), [
                    'customer_id' => $customer->id,
                    'trace' => $e->getTraceAsString()
                ]);

                return redirect()->back()
                    ->withInput()
                    ->withErrors(['images' => 'Resim yükleme sırasında hata oluştu: ' . $e->getMessage()]);
            }
        }

        return redirect()->route('customers.index')->with('success', 'Müşteri başarıyla güncellendi!');
    }

    public function createFromPotential($potentialCustomerId)
    {
        $potential = \App\Models\PotentialCustomer::with(['authorizedPersons', 'phones'])->findOrFail($potentialCustomerId);

        // Ana alan eşleştirmesi
        $prefill = [
            'email' => $potential->email ?? '',
            'company_name' => $potential->company_name ?? '',
            'pluscard_no' => $potential->pluscard_no ?? '',
            'tc' => $potential->tc ?? '',
            'authorized_title' => $potential->authorized_title ?? '',
            'authorized_first_name' => $potential->authorized_name ?? $potential->authorized_first_name,
            'authorized_last_name' => $potential->authorized_lastname ?? $potential->authorized_last_name,
            'authorized_phone' => $potential->authorized_phone ?? '',
            'city' => $potential->city ?? '',
            'district' => $potential->district ?? '',
            'address' => $potential->address ?? '',
        ];

        // Yetkili kişileri hazırla (PotentialCustomer'da name/lastname, Customer'da first_name/last_name)
        $authorizedPersons = [];
        foreach ($potential->authorizedPersons as $person) {
            $authorizedPersons[] = [
                'title' => $person->title ?? '',
                'first_name' => $person->name ?? '', // name -> first_name
                'last_name' => $person->lastname ?? '', // lastname -> last_name
                'phone' => $person->phone ?? '',
            ];
        }

        // Şirket telefonlarını hazırla
        $companyPhones = [];
        foreach ($potential->phones as $phone) {
            $companyPhones[] = [
                'phone' => $phone->phone ?? '',
                'type' => $phone->type ?? 'Sabit',
            ];
        }

        return view('customers.create', compact('prefill', 'authorizedPersons', 'companyPhones'));
    }

    public function registrySearch(Request $request)
    {
        $q = trim($request->input('q'));
        $normalizedPhone = preg_replace('/[^0-9]/', '', $q); // Sadece rakamlar

        $pgCustomer = DB::connection('pgsql')
            ->table('customers')
            ->leftJoin('customer_phones', 'customers.id', '=', 'customer_phones.customer_id')
            ->where(function ($query) use ($normalizedPhone) {
                $query->whereRaw("REGEXP_REPLACE(customers.authorized_phone, '[^0-9]', '', 'g') = ?", [$normalizedPhone])
                    ->orWhereRaw("REGEXP_REPLACE(customers.pluscard_no, '[^0-9]', '', 'g') = ?", [$normalizedPhone])
                    ->orWhereRaw("REGEXP_REPLACE(customers.tc, '[^0-9]', '', 'g') = ?", [$normalizedPhone])
                    ->orWhereRaw("REGEXP_REPLACE(customer_phones.phone, '[^0-9]', '', 'g') = ?", [$normalizedPhone]);
            })
            ->select('customers.*')
            ->first();

        $mysqlCustomer = DB::connection('mysql')
            ->table('customers')
            ->where(function($query) use ($normalizedPhone) {
                $query->whereRaw("REPLACE(REPLACE(REPLACE(telefon, ' ', ''), '+', ''), '-', '') = ?", [$normalizedPhone])
                    ->orWhereRaw("REPLACE(REPLACE(REPLACE(cep, ' ', ''), '+', ''), '-', '') = ?", [$normalizedPhone]);
            })
            ->first();

        if ($pgCustomer || $mysqlCustomer) {
            return response()->json([
                'pgsql' => $pgCustomer,
                'mysql' => $mysqlCustomer,
            ]);
        }

        return response()->json(['message' => 'Kayıt bulunamadı'], 404);
    }

    public function exportExcel(Request $request)
    {
        $query = Customer::with(['phones', 'authorizedPersons']);

        // Filtre parametrelerini al
        $searchValue = $request->get('search');

        // Genel arama
        if ($searchValue) {
            $query->where(function($q) use ($searchValue) {
                $q->where('email', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_title', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_first_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_last_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_phone', 'ilike', "%{$searchValue}%")
                  ->orWhere('company_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('city', 'ilike', "%{$searchValue}%")
                  ->orWhere('district', 'ilike', "%{$searchValue}%");
            });
        }

        // Sütun bazlı filtreler
        if ($request->has('columns')) {
            foreach ($request->get('columns') as $index => $column) {
                if (!empty($column['search']['value'])) {
                    $searchTerm = $column['search']['value'];
                    switch ($index) {
                        case 0: // ID
                            $query->where('id', 'like', "%{$searchTerm}%");
                            break;
                        case 1: // Email
                            $query->where('email', 'ilike', "%{$searchTerm}%");
                            break;
                        case 2: // Authorized Title
                            $query->where('authorized_title', 'ilike', "%{$searchTerm}%");
                            break;
                        case 3: // Authorized First Name
                            $query->where('authorized_first_name', 'ilike', "%{$searchTerm}%");
                            break;
                        case 4: // Authorized Last Name
                            $query->where('authorized_last_name', 'ilike', "%{$searchTerm}%");
                            break;
                        case 5: // Authorized Phone
                            $query->where('authorized_phone', 'ilike', "%{$searchTerm}%");
                            break;
                        case 6: // Company Name
                            $query->where('company_name', 'ilike', "%{$searchTerm}%");
                            break;
                        case 8: // City
                            $query->where('city', 'ilike', "%{$searchTerm}%");
                            break;
                        case 9: // District
                            $query->where('district', 'ilike', "%{$searchTerm}%");
                            break;
                    }
                }
            }
        }

        $customers = $query->get();

        $filename = 'musteriler_' . date('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($customers) {
            $file = fopen('php://output', 'w');

            // UTF-8 BOM ekle
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // Header satırı
            fputcsv($file, [
                'ID',
                'Email',
                'Yetkili Unvan',
                'Yetkili İsim',
                'Yetkili Soyisim',
                'Yetkili Telefon',
                'Şirket İsmi',
                'Şirket Telefonları',
                'İl',
                'İlçe',
                'Kayıt Tarihi'
            ]);

            // Veri satırları
            foreach ($customers as $customer) {
                $phones = $customer->phones->pluck('phone')->implode(', ');

                fputcsv($file, [
                    $customer->id,
                    $customer->email,
                    $customer->authorized_title ?? '-',
                    $customer->authorized_first_name ?? '-',
                    $customer->authorized_last_name ?? '-',
                    $customer->authorized_phone ?? '-',
                    $customer->company_name ?? '-',
                    $phones ?: '-',
                    $customer->city ?? '-',
                    $customer->district ?? '-',
                    $customer->created_at ? $customer->created_at->format('d.m.Y') : '-'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Delete a customer image.
     */
    public function deleteImage($customer_id, $image_id)
    {
        $customer = Customer::findOrFail($customer_id);
        $image = CustomerImage::where('customer_id', $customer->id)->findOrFail($image_id);

        try {
            $image->delete(); // Model'deki boot metodu dosyayı da silecek

            return response()->json([
                'success' => true,
                'message' => 'Resim başarıyla silindi.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Resim silinirken hata oluştu: ' . $e->getMessage()
            ], 500);
        }
    }

}