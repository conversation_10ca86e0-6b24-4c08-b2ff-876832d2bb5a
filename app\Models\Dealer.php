<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Dealer extends Model
{
    use HasFactory;

    protected $fillable = [
        'id',
        'name',
        'contact_person',
        'phone',
        'email',
        'address',
        'city',
        'district',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];



    /**
     * Get the regions for the dealer (many-to-many relationship through pivot table).
     */
    public function regions()
    {
        return $this->belongsToMany(Region::class, 'dealer_regions');
    }

    /**
     * Get the dealer regions pivot records.
     */
    public function dealerRegions()
    {
        return $this->hasMany(DealerRegion::class);
    }

    /**
     * Get the customers for the dealer.
     */
    public function customers()
    {
        return $this->hasMany(Customer::class);
    }

    /**
     * Get the users for the dealer (many-to-many relationship).
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_dealers');
    }

    /**
     * Get the user dealers pivot records.
     */
    public function userDealers()
    {
        return $this->hasMany(UserDealer::class);
    }

    /**
     * Scope a query to only include active dealers.
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Search scope for dealers
     */
    public function scopeSearch($query, $q)
    {
        if (!$q) return $query;

        return $query->where(function($sub) use ($q) {
            $sub->where('name', 'ILIKE', "%$q%")
                ->orWhere('contact_person', 'ILIKE', "%$q%")
                ->orWhere('phone', 'ILIKE', "%$q%")
                ->orWhere('email', 'ILIKE', "%$q%")
                ->orWhere('city', 'ILIKE', "%$q%")
                ->orWhere('district', 'ILIKE', "%$q%")
                ->orWhereHas('regions', function($regionQuery) use ($q) {
                    $regionQuery->where('name', 'ILIKE', "%$q%");
                });
        });
    }
}
