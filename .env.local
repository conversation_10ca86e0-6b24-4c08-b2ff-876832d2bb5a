APP_NAME="UMRAN OTO"
APP_ENV=local
APP_KEY=base64:sVLk6Y5OvCSI9Hb6inQytbl/ZejC5F4dddahthNIQVw=
APP_DEBUG=true
APP_URL=http://crm.umram.local
UMRAM_ONLINE_URL=http://test.umram.online

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=db
DB_PORT=5432
DB_DATABASE=crmumramdb
DB_USERNAME=root
DB_PASSWORD=root

DB_UMRAM_CONNECTION=mysql
DB_UMRAM_HOST=*************
DB_UMRAM_PORT=3306
DB_UMRAM_DATABASE=umramdb
DB_UMRAM_USERNAME=umramdbuser
DB_UMRAM_PASSWORD="1hEvbBKUlNuz"

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null
SESSION_COOKIE=umran_oto_session

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=umran_oto_cache_

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=***********
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_PREFIX=umran_oto_database_

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"
