<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['images', 'title' => 'Resim Galerisi', 'showTitle' => true, 'allowDelete' => false, 'deleteRoute' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['images', 'title' => 'Resim Galerisi', 'showTitle' => true, 'allowDelete' => false, 'deleteRoute' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php if($images && $images->count() > 0): ?>
<div class="image-gallery-container">
    <?php if($showTitle): ?>
    <h6 class="mb-3"><?php echo e($title); ?></h6>
    <?php endif; ?>
    
    <div class="row">
        <?php $__currentLoopData = $images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="col-md-3 col-sm-4 col-6 mb-3">
            <div class="image-gallery-item position-relative">
                <div class="image-wrapper" style="height: 200px; overflow: hidden; border-radius: 8px; cursor: pointer;">
                    <img src="<?php echo e($image->url); ?>" 
                         alt="<?php echo e($image->alt_text ?? $image->filename); ?>" 
                         class="img-fluid w-100 h-100 gallery-image"
                         style="object-fit: cover; transition: transform 0.3s ease;"
                         data-toggle="modal" 
                         data-target="#imageModal"
                         data-image-url="<?php echo e($image->url); ?>"
                         data-image-title="<?php echo e($image->filename); ?>"
                         data-image-description="<?php echo e($image->description); ?>"
                         data-image-size="<?php echo e($image->formatted_size); ?>"
                         data-image-dimensions="<?php echo e($image->width); ?>x<?php echo e($image->height); ?>">
                </div>
                
                <?php if($allowDelete && $deleteRoute): ?>
                <button type="button" 
                        class="btn btn-danger btn-sm position-absolute delete-image-btn" 
                        style="top: 8px; right: 8px; padding: 4px 8px; z-index: 10;"
                        onclick="deleteImage(<?php echo e($image->id); ?>, '<?php echo e($deleteRoute); ?>')"
                        title="Resmi Sil">
                    <i class="fas fa-trash"></i>
                </button>
                <?php endif; ?>
                
                <div class="image-info mt-2">
                    <small class="text-muted d-block"><?php echo e($image->filename); ?></small>
                    <small class="text-muted"><?php echo e($image->formatted_size); ?></small>
                    <?php if($image->description): ?>
                    <small class="text-muted d-block"><?php echo e(Str::limit($image->description, 50)); ?></small>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Resim Görüntüleyici</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="" class="img-fluid" style="max-height: 70vh;">
                <div class="mt-3">
                    <h6 id="modalImageTitle"></h6>
                    <p id="modalImageDescription" class="text-muted"></p>
                    <small class="text-muted">
                        <span id="modalImageSize"></span> | 
                        <span id="modalImageDimensions"></span>
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <a id="modalImageDownload" href="" download class="btn btn-primary">
                    <i class="fas fa-download"></i> İndir
                </a>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Kapat</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.image-gallery-item:hover .image-wrapper img {
    transform: scale(1.05);
}

.image-gallery-item .delete-image-btn {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-gallery-item:hover .delete-image-btn {
    opacity: 1;
}

.gallery-image:hover {
    cursor: pointer;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Modal'ı açarken resim bilgilerini doldur
    $('.gallery-image').on('click', function() {
        const imageUrl = $(this).data('image-url');
        const imageTitle = $(this).data('image-title');
        const imageDescription = $(this).data('image-description');
        const imageSize = $(this).data('image-size');
        const imageDimensions = $(this).data('image-dimensions');
        
        $('#modalImage').attr('src', imageUrl).attr('alt', imageTitle);
        $('#modalImageTitle').text(imageTitle);
        $('#modalImageDescription').text(imageDescription || 'Açıklama bulunmuyor');
        $('#modalImageSize').text(imageSize);
        $('#modalImageDimensions').text(imageDimensions);
        $('#modalImageDownload').attr('href', imageUrl);
    });
});

// Resim silme fonksiyonu
function deleteImage(imageId, deleteRoute) {
    if (confirm('Bu resmi silmek istediğinizden emin misiniz?')) {
        $.ajax({
            url: deleteRoute.replace(':id', imageId),
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    location.reload(); // Sayfayı yenile
                } else {
                    alert('Resim silinirken hata oluştu: ' + (response.message || 'Bilinmeyen hata'));
                }
            },
            error: function(xhr) {
                alert('Resim silinirken hata oluştu: ' + (xhr.responseJSON?.message || 'Bilinmeyen hata'));
            }
        });
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php else: ?>
<div class="text-center text-muted py-4">
    <i class="fas fa-images fa-3x mb-3"></i>
    <p>Henüz resim eklenmemiş.</p>
</div>
<?php endif; ?>
<?php /**PATH /var/www/crm.umram.online/resources/views/components/image-gallery.blade.php ENDPATH**/ ?>