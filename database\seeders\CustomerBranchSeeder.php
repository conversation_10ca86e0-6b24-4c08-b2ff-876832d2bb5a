<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Customer;
use App\Models\CustomerBranch;
use Faker\Factory as Faker;

class CustomerBranchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create('tr_TR');
        
        // Türkiye'deki şehirler ve ilçeler
        $cities = [
            'İstanbul' => ['Kadıköy', 'Beşiktaş', 'Şişli', 'Beyoğlu', 'Üsküdar', 'Bakırköy', '<PERSON><PERSON>tinburnu', 'Fatih'],
            'Ankara' => ['Çankaya', 'Keçiören', 'Yenimahalle', 'Mamak', 'Sincan', 'Etimesgut', 'Gölbaşı'],
            'İzmir' => ['Konak', 'Karşıyaka', 'Bornova', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>aziemir', '<PERSON><PERSON><PERSON><PERSON>'],
            '<PERSON><PERSON><PERSON>' => ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'İnegöl'],
            'Antal<PERSON>' => ['Muratpaşa', 'Kepez', 'Konyaaltı', 'Aksu', 'Döşemealtı', 'Manavgat'],
            'Adana' => ['Seyhan', 'Yüreğir', 'Çukurova', 'Sarıçam', 'Karaisalı'],
            'Konya' => ['Meram', 'Karatay', 'Selçuklu', 'Akşehir', 'Ereğli'],
            'Gaziantep' => ['Şahinbey', 'Şehitkamil', 'Oğuzeli', 'Nizip', 'Araban'],
            'Kayseri' => ['Melikgazi', 'Kocasinan', 'Talas', 'İncesu', 'Develi'],
            'Mersin' => ['Yenişehir', 'Mezitli', 'Toroslar', 'Akdeniz', 'Tarsus'],
        ];

        // Şube türleri
        $branchTypes = [
            'Merkez Şube',
            'Ana Şube', 
            'Bölge Şubesi',
            'Satış Şubesi',
            'Servis Şubesi',
            'Bayi Şubesi',
            'Yetkili Servis',
            'Satış Noktası',
            'Hizmet Merkezi',
            'Dağıtım Merkezi'
        ];

        // Tüm müşterileri al
        $customers = Customer::all();

        foreach ($customers as $customer) {
            // Her müşteri için 0-4 arası şube oluştur
            $branchCount = rand(0, 4);
            
            for ($i = 0; $i < $branchCount; $i++) {
                $cityName = $faker->randomElement(array_keys($cities));
                $districts = $cities[$cityName];
                $districtName = $faker->randomElement($districts);
                
                $branchType = $faker->randomElement($branchTypes);
                $branchNumber = $i + 1;
                
                // Şube adı oluştur
                $branchNames = [
                    $customer->company_name . ' ' . $branchType,
                    $customer->company_name . ' ' . $cityName . ' Şubesi',
                    $customer->company_name . ' ' . $districtName . ' Şubesi',
                    $customer->company_name . ' ' . $branchNumber . '. Şube',
                    $branchType . ' - ' . $cityName,
                ];
                
                $branchName = $faker->randomElement($branchNames);

                CustomerBranch::create([
                    'customer_id' => $customer->id,
                    'branch_name' => $branchName,
                    'email' => $faker->optional(0.7)->email,
                    'phone' => $faker->optional(0.8)->phoneNumber,
                    'city' => $cityName,
                    'district' => $districtName,
                    'address' => $faker->address,
                ]);
            }
        }

        // Bazı büyük müşteriler için ekstra şubeler ekle
        $bigCustomers = Customer::whereNotNull('company_name')
            ->where('company_name', 'like', '%Holding%')
            ->orWhere('company_name', 'like', '%A.Ş.%')
            ->orWhere('company_name', 'like', '%Ltd.%')
            ->limit(5)
            ->get();

        foreach ($bigCustomers as $customer) {
            // Büyük müşteriler için 2-6 ek şube
            $extraBranchCount = rand(2, 6);
            $currentBranchCount = $customer->branches()->count();
            
            for ($i = 0; $i < $extraBranchCount; $i++) {
                $cityName = $faker->randomElement(array_keys($cities));
                $districts = $cities[$cityName];
                $districtName = $faker->randomElement($districts);
                
                $branchNumber = $currentBranchCount + $i + 1;
                
                // Büyük şirketler için daha çeşitli şube isimleri
                $branchNames = [
                    $customer->company_name . ' ' . $cityName . ' Bölge Müdürlüğü',
                    $customer->company_name . ' ' . $cityName . ' Satış Merkezi',
                    $customer->company_name . ' ' . $districtName . ' Temsilciliği',
                    $customer->company_name . ' ' . $cityName . ' Hizmet Merkezi',
                    $customer->company_name . ' ' . $branchNumber . '. Şube',
                    $cityName . ' ' . $faker->randomElement($branchTypes),
                ];
                
                $branchName = $faker->randomElement($branchNames);

                CustomerBranch::create([
                    'customer_id' => $customer->id,
                    'branch_name' => $branchName,
                    'email' => $faker->optional(0.8)->email,
                    'phone' => $faker->optional(0.9)->phoneNumber,
                    'city' => $cityName,
                    'district' => $districtName,
                    'address' => $faker->address,
                ]);
            }
        }
    }
}
