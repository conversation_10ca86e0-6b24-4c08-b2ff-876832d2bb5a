@extends('layouts.index')

@section('content')
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="text-center mb-4">Taki<PERSON> - {{ $customer->company_name }}</h3>
                </div>
                <div class="card-body">
                    @if($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

            <form action="{{ route('customers.customer-followups.store', $customer->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                
                <!-- Visit Information Section -->
                <h5 class="mb-3 text-primary"><PERSON><PERSON><PERSON>ilgiler<PERSON></h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="track_date" class="form-label">Takip Tarihi</label>
                        <input type="date" class="form-control" id="track_date" name="track_date" value="{{ old('track_date', date('Y-m-d')) }}" required>
                        @error('track_date')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="meet_date" class="form-label">Görüşme Tarihi</label>
                        <input type="date" class="form-control" id="meet_date" name="meet_date" value="{{ old('meet_date', date('Y-m-d')) }}">
                        @error('meet_date')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="conversation_type" class="form-label">Görüşme Türü</label>
                        <select class="form-control" id="conversation_type" name="conversation_type" required>
                            <option value="">Seçiniz</option>
                            <option value="telefon" {{ old('conversation_type')==='telefon' ? 'selected' : '' }}>Telefon</option>
                            <option value="yerinde" {{ old('conversation_type')==='yerinde' ? 'selected' : '' }}>Yerinde</option>
                            <option value="bayide" {{ old('conversation_type')==='bayide' ? 'selected' : '' }}>Bayide</option>
                        </select>
                        @error('conversation_type')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="user_name" class="form-label">Ziyaret Eden Kişi <small class="text-muted">(Otomatik)</small></label>
                        <input type="text" class="form-control bg-light" id="user_name" name="user_name" value="{{ old('user_name', $prefill['user_name'] ?? '') }}" readonly>
                        @error('user_name')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label">Durum</label>
                        <select class="form-control" id="status" name="status" required>
                            <option value="">Seçiniz</option>
                            @foreach(\App\Models\CustomerFollowup::$statusOptions as $value => $label)
                                <option value="{{ $value }}" {{ old('status') == $value ? 'selected' : '' }}>{{ $label }}</option>
                            @endforeach
                        </select>
                        @error('status')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="status_at_meeting_date" class="form-label">Görüşme Tarihindeki Durum</label>
                        <select class="form-control" id="status_at_meeting_date" name="status_at_meeting_date">
                            <option value="">Seçiniz</option>
                            @foreach(\App\Models\CustomerFollowup::$statusAtMeetingDateOptions as $value => $label)
                                <option value="{{ $value }}" {{ old('status_at_meeting_date') == $value ? 'selected' : '' }}>{{ $label }}</option>
                            @endforeach
                        </select>
                        @error('status_at_meeting_date')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="work_type" class="form-label">Çalışma Türü</label>
                        <select class="form-control" id="work_type" name="work_type">
                            <option value="">Seçiniz</option>
                            @foreach(\App\Models\CustomerFollowup::$workTypeOptions as $value => $label)
                                <option value="{{ $value }}" {{ old('work_type') == $value ? 'selected' : '' }}>{{ $label }}</option>
                            @endforeach
                        </select>
                        @error('work_type')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                </div>

                <!-- Location Information Section -->
                <h5 class="mb-3 text-primary mt-4">Lokasyon Bilgileri</h5>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="city" class="form-label">Şehir <small class="text-muted">(Otomatik)</small></label>
                        <input type="text" class="form-control bg-light" id="city" name="city" value="{{ old('city', $prefill['city'] ?? '') }}" readonly>
                        @error('city')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="district" class="form-label">İlçe <small class="text-muted">(Otomatik)</small></label>
                        <input type="text" class="form-control bg-light" id="district" name="district" value="{{ old('district', $prefill['district'] ?? '') }}" readonly>
                        @error('district')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="branch_name" class="form-label">Şube Adı <small class="text-muted">(Otomatik)</small></label>
                        <input type="text" class="form-control bg-light" id="branch_name" name="branch_name" value="{{ old('branch_name', $prefill['branch_name'] ?? '') }}" readonly>
                        @error('branch_name')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                </div>

                <!-- Company Information Section -->
                <h5 class="mb-3 text-primary mt-4">Firma Bilgileri</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="company_name" class="form-label">Firma Adı <small class="text-muted">(Otomatik)</small></label>
                        <input type="text" class="form-control bg-light" id="company_name" name="company_name" value="{{ old('company_name', $prefill['company_name'] ?? '') }}" readonly>
                        @error('company_name')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="current_firm" class="form-label">Mevcut Firma</label>
                        <input type="text" class="form-control" id="current_firm" name="current_firm" value="{{ old('current_firm') }}">
                        @error('current_firm')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="mail" class="form-label">E-posta <small class="text-muted">(Otomatik)</small></label>
                        <input type="email" class="form-control bg-light" id="mail" name="mail" value="{{ old('mail', $prefill['mail'] ?? '') }}" readonly>
                        @error('mail')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                </div>

                <!-- Contact Person Information Section -->
                <h5 class="mb-3 text-primary mt-4">Görüşülen Kişi Bilgileri</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="contact_first_name" class="form-label">Görüşülen Kişi Adı</label>
                        <input type="text" class="form-control" id="contact_first_name" name="contact_first_name" value="{{ old('contact_first_name') }}">
                        @error('contact_first_name')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="contact_last_name" class="form-label">Görüşülen Kişi Soyadı</label>
                        <input type="text" class="form-control" id="contact_last_name" name="contact_last_name" value="{{ old('contact_last_name') }}">
                        @error('contact_last_name')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="contact_title" class="form-label">Görüşülen Kişi Unvanı</label>
                        <input type="text" class="form-control" id="contact_title" name="contact_title" value="{{ old('contact_title') }}">
                        @error('contact_title')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="contact_phone" class="form-label">Görüşülen Kişi Telefonu</label>
                        <input type="text" class="form-control inputmask" id="contact_phone" name="contact_phone" value="{{ old('contact_phone') }}" data-inputmask="'mask': '09999999999'"   inputmode="tel"  pattern="0[0-9]{10}" placeholder="0##########">
                        @error('contact_phone')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="contact_email" class="form-label">Görüşülen Kişi Emaili</label>
                        <input type="email" class="form-control" id="contact_email" name="contact_email" value="{{ old('contact_email') }}">
                        @error('contact_email')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                </div>

                <!-- Branch Information Section -->
                <h5 class="mb-3 text-primary mt-4">Şube Bilgileri</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="current_branch_count" class="form-label">Mevcut Şube Sayısı</label>
                        <input type="number" class="form-control" id="current_branch_count" name="current_branch_count" value="{{ old('current_branch_count') }}">
                        @error('current_branch_count')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="branch_potential" class="form-label">Şube Potansiyeli</label>
                        <input type="number" class="form-control" id="branch_potential" name="branch_potential" value="{{ old('branch_potential') }}">
                        @error('branch_potential')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                </div>

                <!-- Agreement Information Section -->
                <h5 class="mb-3 text-primary mt-4">Anlaşma Bilgileri</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="agreement_status" class="form-label">Anlaşma Sağlandı mı?</label>
                        <select class="form-control" id="agreement_status" name="agreement_status">
                            <option value="">Seçiniz</option>
                            <option value="1" {{ old('agreement_status')==='1' ? 'selected' : '' }}>Evet</option>
                            <option value="0" {{ old('agreement_status')==='0' ? 'selected' : '' }}>Hayır</option>
                        </select>
                        @error('agreement_status')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                    <div class="col-md-6 mb-3" id="pluscard-field">
                        <label class="form-label">Pluscard yükleme yapıldı mı?</label>
                        <select class="form-control" id="pluscard_been_loaded" name="pluscard_been_loaded">
                            <option value="">Seçiniz</option>
                            <option value="1" {{ old('pluscard_been_loaded')==='1' ? 'selected' : '' }}>Evet</option>
                            <option value="0" {{ old('pluscard_been_loaded')==='0' ? 'selected' : '' }}>Hayır</option>
                        </select>
                        @error('pluscard_been_loaded')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                </div>
                <div id="pluscard-evet-fields" style="display:none;">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="number_of_customers_loaded" class="form-label">Yükleme Yapılan Müşteri Sayısı</label>
                            <input type="number" class="form-control" id="number_of_customers_loaded" name="number_of_customers_loaded" min="1" value="{{ old('number_of_customers_loaded') }}">
                            @error('number_of_customers_loaded')<div class="text-danger">{{ $message }}</div>@enderror
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="loading_amount" class="form-label">Yükleme Miktarı</label>
                            <input type="number" step="0.01" class="form-control" id="loading_amount" name="loading_amount" value="{{ old('loading_amount') }}">
                            @error('loading_amount')<div class="text-danger">{{ $message }}</div>@enderror
                        </div>
                    </div>
                </div>
                <div id="reason-not-understanding-field" style="display:none;">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="reason_not_understanding" class="form-label">Anlaşamama Sebebi</label>
                            <select class="form-control" id="reason_not_understanding" name="reason_not_understanding">
                                <option value="">Seçiniz</option>
                                <option value="Fiyat yüksek" {{ old('reason_not_understanding')==='Fiyat yüksek' ? 'selected' : '' }}>Fiyat yüksek</option>
                                <option value="Mesafe Uzak" {{ old('reason_not_understanding')==='Mesafe Uzak' ? 'selected' : '' }}>Mesafe Uzak</option>
                                <option value="Bayi ile yaşanan sorunlar" {{ old('reason_not_understanding')==='Bayi ile yaşanan sorunlar' ? 'selected' : '' }}>Bayi ile yaşanan sorunlar</option>
                                <option value="Ekpertize ihtiyaç duymuyor" {{ old('reason_not_understanding')==='Ekpertize ihtiyaç duymuyor' ? 'selected' : '' }}>Ekpertize ihtiyaç duymuyor</option>
                                <option value="Kendisi yapıyor" {{ old('reason_not_understanding')==='Kendisi yapıyor' ? 'selected' : '' }}>Kendisi yapıyor</option>
                                <option value="Başka ekspertize yaptırıyor" {{ old('reason_not_understanding')==='Başka ekspertize yaptırıyor' ? 'selected' : '' }}>Başka ekspertize yaptırıyor</option>
                                <option value="Değerlendirme" {{ old('reason_not_understanding')==='Değerlendirme' ? 'selected' : '' }}>Değerlendirme</option>
                            </select>
                            @error('reason_not_understanding')<div class="text-danger">{{ $message }}</div>@enderror
                        </div>
                    </div>
                </div>

                <!-- Notes Section -->
                <h5 class="mb-3 text-primary mt-4">Notlar</h5>
                <div class="row" id="note-row">
                    <div class="col-12 mb-3">
                        <label for="note" class="form-label">Not</label>
                        <textarea class="form-control" id="note" name="note" required>{{ old('note') }}</textarea>
                        @error('note')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 mb-3">
                        <label for="description" class="form-label">Açıklama</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ old('description') }}</textarea>
                        @error('description')<div class="text-danger">{{ $message }}</div>@enderror
                    </div>
                </div>

                <script>
                document.addEventListener('DOMContentLoaded', function() {
                    function toggleAgreementFields() {
                        var agreementStatus = document.getElementById('agreement_status').value;
                        var pluscardField = document.getElementById('pluscard-field');
                        var pluscardSelect = document.getElementById('pluscard_been_loaded');
                        var reasonField = document.getElementById('reason-not-understanding-field');
                        var reasonSelect = document.getElementById('reason_not_understanding');

                        if (agreementStatus === '0') {
                            // Anlaşma yok - PlusCard alanını gizle, anlamama nedeni göster
                            pluscardField.style.display = 'none';
                            pluscardSelect.value = '0';
                            pluscardSelect.removeAttribute('required');
                            reasonField.style.display = '';
                            reasonSelect.setAttribute('required', 'required');
                            document.getElementById('pluscard-evet-fields').style.display = 'none';
                        } else if (agreementStatus === '1') {
                            // Anlaşma var - PlusCard alanını göster
                            pluscardField.style.display = '';
                            pluscardSelect.setAttribute('required', 'required');
                            reasonField.style.display = 'none';
                            reasonSelect.removeAttribute('required');
                            reasonSelect.value = '';
                            togglePluscardFields();
                        } else {
                            // Seçim yok - her şeyi gizle
                            pluscardField.style.display = 'none';
                            pluscardSelect.removeAttribute('required');
                            reasonField.style.display = 'none';
                            reasonSelect.removeAttribute('required');
                            document.getElementById('pluscard-evet-fields').style.display = 'none';
                        }
                    }

                    function togglePluscardFields() {
                        var agreementStatus = document.getElementById('agreement_status').value;
                        var reasonField = document.getElementById('reason-not-understanding-field');
                        var reasonSelect = document.getElementById('reason_not_understanding');

                        if (agreementStatus === '1') {
                            var val = document.getElementById('pluscard_been_loaded').value;
                            document.getElementById('pluscard-evet-fields').style.display = (val === '1') ? '' : 'none';

                            // PlusCard yüklenmedi (0) ise anlamama nedeni alanını göster
                            if (val === '0') {
                                reasonField.style.display = '';
                                reasonSelect.setAttribute('required', 'required');
                            } else {
                                reasonField.style.display = 'none';
                                reasonSelect.removeAttribute('required');
                                reasonSelect.value = ''; // Değeri temizle
                            }
                        }
                    }

                    document.getElementById('agreement_status').addEventListener('change', toggleAgreementFields);
                    document.getElementById('pluscard_been_loaded').addEventListener('change', togglePluscardFields);

                    toggleAgreementFields();
                });
                </script>

                <!-- Resim Galerisi -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group mb-3">
                            <label for="images" class="form-label">Resim Galerisi</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="images" name="images[]" multiple accept="image/*">
                                <label class="custom-file-label" for="images">Resim dosyalarını seçin...</label>
                            </div>
                            <small class="form-text text-muted">
                                Birden fazla resim seçebilirsiniz. Maksimum dosya boyutu: 10MB. Desteklenen formatlar: JPEG, PNG, GIF, WebP
                            </small>
                            @error('images')<div class="text-danger">{{ $message }}</div>@enderror
                            @error('images.*')<div class="text-danger">{{ $message }}</div>@enderror
                        </div>

                        <!-- Resim Önizleme Alanı -->
                        <div id="image-preview-container" class="row" style="display: none;">
                            <div class="col-12">
                                <h6>Seçilen Resimler:</h6>
                                <div id="image-preview" class="d-flex flex-wrap gap-2"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-success w-100">Kaydet</button>
                        <a href="{{ route('customers.customer-followups.index', $customer->id) }}" class="btn btn-secondary w-100 mt-2">Geri Dön</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
@push('scripts')
    <script src="{{asset('assets')}}/plugins/inputmask/jquery.inputmask.bundle.js"></script>
    <script>
        $(document).ready(function(){
            $(".inputmask").inputmask({
                mask: "0##########",
                showMaskOnHover: false,
                showMaskOnFocus: true,
                clearIncomplete: true,
                definitions: {
                    '9': {
                        validator: "[0-9]",
                        cardinality: 1,
                        definitionSymbol: "9"
                    }
                },
                onBeforePaste: function (pastedValue, opts) {
                    return pastedValue.replace(/[^\d\+]/g, '');
                },
                onKeyDown: function(e, buffer, caretPos, opts) {
                    var key = e.key;
                    if (!/[0-9]/.test(key) && key.length === 1) {
                        e.preventDefault();
                    }
                }
            });
            // Resim önizleme işlevselliği
            $('#images').on('change', function(e) {
            const files = e.target.files;
            const previewContainer = $('#image-preview');
            const previewContainerWrapper = $('#image-preview-container');

            // Önizleme alanını temizle
            previewContainer.empty();

            if (files.length > 0) {
                previewContainerWrapper.show();

                Array.from(files).forEach((file, index) => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const imagePreview = $(`
                                <div class="position-relative" style="width: 120px; height: 120px;">
                                    <img src="${e.target.result}" class="img-thumbnail" style="width: 100%; height: 100%; object-fit: cover;">
                                    <button type="button" class="btn btn-danger btn-sm position-absolute" style="top: 5px; right: 5px; padding: 2px 6px;" onclick="removeImagePreview(this, ${index})">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <div class="text-center mt-1">
                                        <small class="text-muted">${file.name}</small>
                                    </div>
                                </div>
                            `);
                            previewContainer.append(imagePreview);
                        };
                        reader.readAsDataURL(file);
                    }
                });
            } else {
                previewContainerWrapper.hide();
            }
        });

            // Custom file input label güncelleme
            $('.custom-file-input').on('change', function() {
                const files = this.files;
                let fileName = '';
                if (files.length === 1) {
                    fileName = files[0].name;
                } else if (files.length > 1) {
                    fileName = files.length + ' dosya seçildi';
                } else {
                    fileName = 'Resim dosyalarını seçin...';
                }
                $(this).next('.custom-file-label').text(fileName);
            });
        });

    // Resim önizlemesini kaldırma fonksiyonu
    function removeImagePreview(button, index) {
        $(button).closest('.position-relative').remove();

        // File input'tan dosyayı kaldır
        const fileInput = document.getElementById('images');
        const dt = new DataTransfer();
        const files = fileInput.files;

        for (let i = 0; i < files.length; i++) {
            if (i !== index) {
                dt.items.add(files[i]);
            }
        }

        fileInput.files = dt.files;

        // Label'ı güncelle
        const remainingFiles = dt.files.length;
        let fileName = '';
        if (remainingFiles === 1) {
            fileName = dt.files[0].name;
        } else if (remainingFiles > 1) {
            fileName = remainingFiles + ' dosya seçildi';
        } else {
            fileName = 'Resim dosyalarını seçin...';
            $('#image-preview-container').hide();
        }
        $('.custom-file-label').text(fileName);
    }
    </script>
@endpush