<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Customer;
use App\Models\CustomerPhone;
use App\Models\Dealer;
use Illuminate\Support\Facades\Hash;
use Faker\Factory as Faker;
use Illuminate\Support\Facades\DB;

class CustomerSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('customers')->truncate();
        DB::table('customer_phones')->truncate();
        $faker = Faker::create('tr_TR');
        $cities = [
            'İstanbul' => ['Kadıköy', 'Beşiktaş', 'Üsküdar', 'Beylikdüzü', 'Avcılar', 'Bakırköy', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>'],
            'Ankara' => ['Çankaya', 'Keçiören', 'Yenimahalle', 'Mamak', 'Altındağ', 'Etimesgut', 'Sincan'],
            'İzmir' => ['<PERSON>na<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'Born<PERSON>', '<PERSON><PERSON>', '<PERSON>san<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>rl<PERSON>'],
            '<PERSON><PERSON><PERSON>' => ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'İneg<PERSON><PERSON>', 'Mudanya', 'Gemlik', 'Orhangazi'],
            'Antalya' => ['Muratpaşa', 'Kepez', 'Alanya', 'Manavgat', 'Konyaaltı', 'Aksu', 'Döşemealtı'],
            'Adana' => ['Seyhan', 'Çukurova', 'Sarıçam', 'Yüreğir', 'Karaisalı', 'Pozantı'],
            'Konya' => ['Selçuklu', 'Meram', 'Karatay', 'Ereğli', 'Akşehir', 'Beyşehir'],
            'Gaziantep' => ['Şahinbey', 'Şehitkamil', 'Oğuzeli', 'Nizip', 'İslahiye', 'Araban']
        ];
        
        $titles = ['Genel Müdür', 'Satış Müdürü', 'Operasyon Müdürü', 'Pazarlama Müdürü', 'İşletme Müdürü', 'Bölge Müdürü', 'Şube Müdürü', 'Yönetici'];

        // Bayileri al (varsa)
        $dealers = Dealer::all();

        for ($i = 0; $i < 50; $i++) {
            $city = $faker->randomElement(array_keys($cities));
            $district = $faker->randomElement($cities[$city]);
            $firstName = $faker->firstName;
            $lastName = $faker->lastName;

            $customer = Customer::create([
                'email' => $faker->unique()->safeEmail,
                'tc' => $faker->unique()->numerify('###########'),
                'pluscard_no' => $faker->unique()->numerify('################'),
                'authorized_title' => $faker->randomElement($titles),
                'authorized_first_name' => $firstName,
                'authorized_last_name' => $lastName,
                'authorized_phone' => $faker->numerify('+90 5## ### ## ##'),
                'company_name' => $faker->company . ' ' . $faker->randomElement(['Ltd. Şti.', 'A.Ş.', 'Sanayi ve Ticaret Ltd. Şti.', 'Ticaret A.Ş.']),
                'city' => $city,
                'district' => $district,
                'address' => $faker->streetAddress . ', ' . $district . '/' . $city,
                'website_url' => $faker->optional(0.6)->url,
                'google_maps_url' => $faker->optional(0.4)->url,
                'listing_url' => $faker->optional(0.3)->url,
                'dealer_id' => $dealers->count() > 0 ? $faker->optional(0.7)->randomElement($dealers)?->id : null,
            ]);

            $phoneTypes = ['Sabit', 'Mobil', 'Fax', 'Diğer'];

            CustomerPhone::create([
                'customer_id' => $customer->id,
                'phone' => $faker->numerify('+90 2## ### ## ##'),
                'type' => 'Sabit'
            ]);

            CustomerPhone::create([
                'customer_id' => $customer->id,
                'phone' => $faker->randomElement([
                    $faker->numerify('+90 5## ### ## ##'),
                    $faker->numerify('+90 2## ### ## ##')
                ]),
                'type' => $faker->randomElement(['Mobil', 'Fax'])
            ]);
        }
    }
} 